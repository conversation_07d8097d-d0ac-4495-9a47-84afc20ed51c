import { useState, useEffect } from 'react'
import './App.css'
import { getTeams, getTeamById, getRiderById, searchRiders } from './services/cyclingData'

function App() {
  const [currentView, setCurrentView] = useState('teams') // 'teams', 'team-detail', 'rider-detail'
  const [teams, setTeams] = useState([])
  const [selectedTeam, setSelectedTeam] = useState(null)
  const [selectedRider, setSelectedRider] = useState(null)
  const [loading, setLoading] = useState(false)
  const [searchQuery, setSearchQuery] = useState('')
  const [searchResults, setSearchResults] = useState([])

  useEffect(() => {
    loadTeams()
  }, [])

  const loadTeams = async () => {
    setLoading(true)
    try {
      const teamsData = await getTeams()
      setTeams(teamsData)
    } catch (error) {
      console.error('Error loading teams:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleTeamClick = async (teamId) => {
    setLoading(true)
    try {
      const teamData = await getTeamById(teamId)
      setSelectedTeam(teamData)
      setCurrentView('team-detail')
    } catch (error) {
      console.error('Error loading team:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleRiderClick = async (riderId) => {
    setLoading(true)
    try {
      const riderData = await getRiderById(riderId)
      setSelectedRider(riderData)
      setCurrentView('rider-detail')
    } catch (error) {
      console.error('Error loading rider:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleSearch = async (query) => {
    if (query.length > 2) {
      setLoading(true)
      try {
        const results = await searchRiders(query)
        setSearchResults(results)
      } catch (error) {
        console.error('Error searching:', error)
      } finally {
        setLoading(false)
      }
    } else {
      setSearchResults([])
    }
  }

  const goBack = () => {
    if (currentView === 'rider-detail') {
      setCurrentView('team-detail')
    } else {
      setCurrentView('teams')
    }
  }

  const getCountryFlag = (countryCode) => {
    const flags = {
      'AE': '🇦🇪', 'NL': '🇳🇱', 'GB': '🇬🇧', 'BE': '🇧🇪',
      'SI': '🇸🇮', 'PT': '🇵🇹', 'DK': '🇩🇰', 'CO': '🇨🇴',
      'FR': '🇫🇷'
    }
    return flags[countryCode] || '🏁'
  }

  return (
    <div className="app">
      <header className="app-header">
        <h1>🚴‍♂️ KGR Cycling</h1>
        <p>Equipos de Ciclismo Profesional</p>

        <div className="search-container">
          <input
            type="text"
            placeholder="Buscar ciclistas..."
            value={searchQuery}
            onChange={(e) => {
              setSearchQuery(e.target.value)
              handleSearch(e.target.value)
            }}
            className="search-input"
          />
          {searchResults.length > 0 && (
            <div className="search-results">
              {searchResults.map(rider => (
                <div
                  key={rider.id}
                  className="search-result-item"
                  onClick={() => handleRiderClick(rider.id)}
                >
                  <span>{getCountryFlag(rider.nationality)} {rider.name}</span>
                  <small>{rider.team}</small>
                </div>
              ))}
            </div>
          )}
        </div>

        {currentView !== 'teams' && (
          <button onClick={goBack} className="back-button">
            ← Volver
          </button>
        )}
      </header>

      <main className="app-main">
        {loading && <div className="loading">Cargando...</div>}

        {currentView === 'teams' && <TeamsView teams={teams} onTeamClick={handleTeamClick} getCountryFlag={getCountryFlag} />}
        {currentView === 'team-detail' && selectedTeam && <TeamDetailView team={selectedTeam} onRiderClick={handleRiderClick} getCountryFlag={getCountryFlag} />}
        {currentView === 'rider-detail' && selectedRider && <RiderDetailView rider={selectedRider} getCountryFlag={getCountryFlag} />}
      </main>
    </div>
  )
}

// Componente para mostrar la lista de equipos
function TeamsView({ teams, onTeamClick, getCountryFlag }) {
  return (
    <div className="teams-grid">
      {teams.map(team => (
        <div key={team.id} className="team-card" onClick={() => onTeamClick(team.id)}>
          <div className="team-header">
            <img src={team.logo} alt={team.name} className="team-logo" />
            <div className="team-info">
              <h3>{team.name}</h3>
              <p>{getCountryFlag(team.nationality)} {team.abbreviation} • {team.status}</p>
            </div>
          </div>
          <div className="team-stats">
            <div className="stat">
              <span className="stat-label">Ranking PCS:</span>
              <span className="stat-value">#{team.pcs_ranking_position}</span>
            </div>
            <div className="stat">
              <span className="stat-label">Puntos:</span>
              <span className="stat-value">{team.pcs_points}</span>
            </div>
            <div className="stat">
              <span className="stat-label">Victorias:</span>
              <span className="stat-value">{team.wins_count}</span>
            </div>
            <div className="stat">
              <span className="stat-label">Ciclistas:</span>
              <span className="stat-value">{team.riders_count}</span>
            </div>
          </div>
          <div className="team-footer">
            <span className="bike-brand">🚴‍♂️ {team.bike}</span>
          </div>
        </div>
      ))}
    </div>
  )
}

// Componente para mostrar detalles del equipo
function TeamDetailView({ team, onRiderClick, getCountryFlag }) {
  return (
    <div className="team-detail">
      <div className="team-detail-header">
        <img src={team.logo} alt={team.name} className="team-logo-large" />
        <div className="team-detail-info">
          <h2>{team.name}</h2>
          <p>{getCountryFlag(team.nationality)} {team.nationality} • {team.status} • {team.abbreviation}</p>
          <div className="team-detail-stats">
            <div className="stat-item">
              <span className="stat-number">{team.pcs_ranking_position}</span>
              <span className="stat-label">Ranking PCS</span>
            </div>
            <div className="stat-item">
              <span className="stat-number">{team.pcs_points}</span>
              <span className="stat-label">Puntos PCS</span>
            </div>
            <div className="stat-item">
              <span className="stat-number">{team.wins_count}</span>
              <span className="stat-label">Victorias</span>
            </div>
            <div className="stat-item">
              <span className="stat-number">{team.uci_ranking_position}</span>
              <span className="stat-label">Ranking UCI</span>
            </div>
          </div>
          <p className="bike-info">🚴‍♂️ Bicicletas: <strong>{team.bike}</strong></p>
        </div>
      </div>

      <div className="riders-section">
        <h3>Ciclistas del Equipo ({team.riders.length})</h3>
        <div className="riders-grid">
          {team.riders.map(rider => (
            <div key={rider.id} className="rider-card" onClick={() => onRiderClick(rider.id)}>
              <div className="rider-header">
                <h4>{rider.name}</h4>
                <span className="rider-nationality">{getCountryFlag(rider.nationality)}</span>
              </div>
              <div className="rider-info">
                <p><strong>Edad:</strong> {rider.age} años</p>
                <p><strong>Ranking:</strong> #{rider.ranking_position}</p>
                <p><strong>Puntos:</strong> {rider.ranking_points}</p>
              </div>
              <div className="rider-specialities">
                {rider.specialities && (
                  <div className="speciality-bars">
                    <div className="speciality">
                      <span>GC</span>
                      <div className="bar">
                        <div className="fill" style={{ width: `${(rider.specialities.gc / 1200) * 100}%` }}></div>
                      </div>
                    </div>
                    <div className="speciality">
                      <span>Montaña</span>
                      <div className="bar">
                        <div className="fill" style={{ width: `${(rider.specialities.climber / 1200) * 100}%` }}></div>
                      </div>
                    </div>
                    <div className="speciality">
                      <span>Sprint</span>
                      <div className="bar">
                        <div className="fill" style={{ width: `${(rider.specialities.sprint / 1200) * 100}%` }}></div>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  )
}

// Componente para mostrar detalles del ciclista
function RiderDetailView({ rider, getCountryFlag }) {
  const formatDate = (dateString) => {
    const date = new Date(dateString)
    return date.toLocaleDateString('es-ES', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    })
  }

  const calculateAge = (birthdate) => {
    const today = new Date()
    const birth = new Date(birthdate)
    let age = today.getFullYear() - birth.getFullYear()
    const monthDiff = today.getMonth() - birth.getMonth()
    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birth.getDate())) {
      age--
    }
    return age
  }

  return (
    <div className="rider-detail">
      <div className="rider-detail-header">
        <div className="rider-avatar">
          <span className="rider-initials">
            {rider.name.split(' ').map(n => n[0]).join('')}
          </span>
        </div>
        <div className="rider-detail-info">
          <h2>{rider.name}</h2>
          <p>{getCountryFlag(rider.nationality)} {rider.nationality}</p>
          <p><strong>Equipo:</strong> {rider.team}</p>
          <div className="rider-basic-info">
            <div className="info-item">
              <span className="info-label">Edad:</span>
              <span className="info-value">{calculateAge(rider.birthdate)} años</span>
            </div>
            <div className="info-item">
              <span className="info-label">Nacimiento:</span>
              <span className="info-value">{formatDate(rider.birthdate)}</span>
            </div>
            {rider.place_of_birth && (
              <div className="info-item">
                <span className="info-label">Lugar:</span>
                <span className="info-value">{rider.place_of_birth}</span>
              </div>
            )}
            {rider.height && (
              <div className="info-item">
                <span className="info-label">Altura:</span>
                <span className="info-value">{rider.height}m</span>
              </div>
            )}
            {rider.weight && (
              <div className="info-item">
                <span className="info-label">Peso:</span>
                <span className="info-value">{rider.weight}kg</span>
              </div>
            )}
          </div>
        </div>
      </div>

      <div className="rider-stats-section">
        <div className="ranking-stats">
          <div className="stat-card">
            <span className="stat-number">#{rider.ranking_position}</span>
            <span className="stat-label">Ranking Mundial</span>
          </div>
          <div className="stat-card">
            <span className="stat-number">{rider.ranking_points}</span>
            <span className="stat-label">Puntos Actuales</span>
          </div>
          <div className="stat-card">
            <span className="stat-number">{rider.career_points}</span>
            <span className="stat-label">Puntos Carrera</span>
          </div>
        </div>

        {rider.specialities && (
          <div className="specialities-section">
            <h3>Especialidades</h3>
            <div className="specialities-chart">
              {Object.entries(rider.specialities).map(([key, value]) => {
                const labels = {
                  one_day_races: 'Clásicas',
                  gc: 'General',
                  time_trial: 'Contrarreloj',
                  sprint: 'Sprint',
                  climber: 'Montaña',
                  hills: 'Colinas'
                }
                return (
                  <div key={key} className="speciality-item">
                    <span className="speciality-name">{labels[key]}</span>
                    <div className="speciality-bar">
                      <div
                        className="speciality-fill"
                        style={{ width: `${(value / 1200) * 100}%` }}
                      ></div>
                    </div>
                    <span className="speciality-value">{value}</span>
                  </div>
                )
              })}
            </div>
          </div>
        )}

        {rider.teams_history && rider.teams_history.length > 0 && (
          <div className="teams-history-section">
            <h3>Historial de Equipos</h3>
            <div className="teams-history">
              {rider.teams_history.map((teamHistory, index) => (
                <div key={index} className="team-history-item">
                  <span className="team-name">{teamHistory.team}</span>
                  <span className="team-period">{teamHistory.season}</span>
                  <span className="team-class">{teamHistory.class}</span>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>
    </div>
  )
}

export default App
