
# 🚴‍♂️ KGR Cycling App

Una aplicación web moderna para explorar equipos de ciclismo profesional y sus ciclistas, inspirada en los datos de ProCyclingStats.

## ✨ Características

- **Vista de Equipos**: Explora los principales equipos de ciclismo profesional con información detallada
- **Detalles del Equipo**: Ve información completa de cada equipo incluyendo:
  - Ranking PCS y UCI
  - Puntos y victorias
  - Marca de bicicletas
  - Lista completa de ciclistas
- **Perfiles de Ciclistas**: Información detallada de cada ciclista:
  - Datos personales (edad, nacionalidad, físico)
  - Especialidades (GC, montaña, sprint, etc.)
  - Historial de equipos
  - Puntos y ranking
- **Búsqueda**: Busca ciclistas por nombre o nacionalidad
- **Navegación Intuitiva**: Interfaz moderna y responsive

## 🚀 Tecnologías Utilizadas

- **React 19** - Framework de frontend
- **Vite** - Herramienta de build y desarrollo
- **CSS3** - Estilos modernos con gradientes y animaciones
- **JavaScript ES6+** - Lógica de la aplicación

## 📦 Instalación y Uso

### Prerrequisitos
- Node.js (versión 16 o superior)
- npm o yarn

### Instalación
```bash
# Clonar el repositorio
git clone <url-del-repositorio>

# Navegar al directorio del proyecto
cd kgrciclyng/kgrcyling

# Instalar dependencias
npm install

# Iniciar el servidor de desarrollo
npm run dev
```

La aplicación estará disponible en `http://localhost:5173` (o el puerto que Vite asigne automáticamente).

### Scripts Disponibles
- `npm run dev` - Inicia el servidor de desarrollo
- `npm run build` - Construye la aplicación para producción
- `npm run preview` - Previsualiza la build de producción
- `npm run lint` - Ejecuta el linter

## 🏗️ Estructura del Proyecto

```
kgrcyling/
├── src/
│   ├── App.jsx          # Componente principal
│   ├── App.css          # Estilos principales
│   ├── main.jsx         # Punto de entrada
│   └── services/
│       └── cyclingData.js # Servicio de datos mock
├── public/              # Archivos estáticos
├── package.json         # Dependencias y scripts
└── README.md           # Este archivo
```

## 📊 Datos

La aplicación utiliza datos simulados basados en información real de equipos de ciclismo profesional como:

- **UAE Team Emirates** - Con ciclistas como Tadej Pogačar, João Almeida, Adam Yates
- **Team Jumbo-Visma** - Con Jonas Vingegaard, Wout van Aert
- **INEOS Grenadiers** - Con Egan Bernal, Geraint Thomas
- **Quick-Step Alpha Vinyl** - Con Remco Evenepoel, Julian Alaphilippe

## 🎨 Características de Diseño

- **Diseño Responsive**: Se adapta a dispositivos móviles y desktop
- **Gradientes Modernos**: Uso de gradientes CSS para un look profesional
- **Animaciones Suaves**: Transiciones y hover effects
- **Tipografía Clara**: Fuentes legibles y jerarquía visual
- **Banderas de Países**: Emojis de banderas para representar nacionalidades
- **Barras de Progreso**: Visualización de especialidades de ciclistas

## 🔮 Futuras Mejoras

- Integración con la API real de ProCyclingStats
- Filtros avanzados por nacionalidad, especialidad, etc.
- Gráficos de rendimiento histórico
- Comparación entre ciclistas
- Información de carreras y resultados
- Sistema de favoritos
- Modo oscuro

## 🤝 Contribución

Las contribuciones son bienvenidas. Para contribuir:

1. Fork el proyecto
2. Crea una rama para tu feature (`git checkout -b feature/AmazingFeature`)
3. Commit tus cambios (`git commit -m 'Add some AmazingFeature'`)
4. Push a la rama (`git push origin feature/AmazingFeature`)
5. Abre un Pull Request

## 📄 Licencia

Este proyecto está bajo la Licencia MIT. Ver el archivo `LICENSE` para más detalles.

## 🙏 Agradecimientos

- Datos inspirados en [ProCyclingStats](https://www.procyclingstats.com/)
- Iconos y emojis de Unicode
- Comunidad de React y Vite
