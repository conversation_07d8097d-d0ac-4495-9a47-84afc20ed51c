// Servicio de datos mock para equipos de ciclismo
// Basado en datos reales de ProCyclingStats

export const teamsData = [
  {
    id: 1,
    name: "UAE Team Emirates",
    nationality: "AE",
    status: "WT",
    abbreviation: "UAD",
    bike: "Colna<PERSON>",
    wins_count: 45,
    pcs_points: 8542,
    pcs_ranking_position: 1,
    uci_ranking_position: 1,
    logo: "https://via.placeholder.com/100x60/FF6B35/FFFFFF?text=UAE",
    riders: [
      {
        id: 1,
        name: "<PERSON><PERSON>",
        nationality: "SI",
        age: 25,
        birthdate: "1998-09-21",
        place_of_birth: "Komenda",
        height: 1.76,
        weight: 66,
        career_points: 4521,
        ranking_points: 2981,
        ranking_position: 1,
        specialities: {
          one_day_races: 850,
          gc: 1200,
          time_trial: 400,
          sprint: 150,
          climber: 1100,
          hills: 900
        },
        teams_history: [
          { team: "UAE Team Emirates", season: "2019-2024", class: "WT" },
          { team: "UAE Team Emirates U23", season: "2018", class: "CT" }
        ]
      },
      {
        id: 2,
        name: "<PERSON>",
        nationality: "<PERSON>",
        age: 26,
        birthdate: "1998-08-05",
        place_of_birth: "<PERSON><PERSON> da Rainha",
        height: 1.72,
        weight: 62,
        career_points: 2156,
        ranking_points: 1245,
        ranking_position: 15,
        specialities: {
          one_day_races: 200,
          gc: 800,
          time_trial: 350,
          sprint: 50,
          climber: 600,
          hills: 400
        }
      },
      {
        id: 3,
        name: "Adam Yates",
        nationality: "GB",
        age: 32,
        birthdate: "1992-08-07",
        place_of_birth: "Bury",
        height: 1.73,
        weight: 61,
        career_points: 3245,
        ranking_points: 1156,
        ranking_position: 18,
        specialities: {
          one_day_races: 300,
          gc: 750,
          time_trial: 200,
          sprint: 30,
          climber: 850,
          hills: 650
        }
      }
    ]
  },
  {
    id: 2,
    name: "Team Jumbo-Visma",
    nationality: "NL",
    status: "WT",
    abbreviation: "TJV",
    bike: "Cervélo",
    wins_count: 38,
    pcs_points: 7892,
    pcs_ranking_position: 2,
    uci_ranking_position: 2,
    logo: "https://via.placeholder.com/100x60/FFD700/000000?text=TJV",
    riders: [
      {
        id: 4,
        name: "Jonas Vingegaard",
        nationality: "DK",
        age: 27,
        birthdate: "1996-12-10",
        place_of_birth: "Hillerød",
        height: 1.75,
        weight: 60,
        career_points: 3456,
        ranking_points: 2456,
        ranking_position: 2,
        specialities: {
          one_day_races: 400,
          gc: 1100,
          time_trial: 450,
          sprint: 80,
          climber: 950,
          hills: 700
        }
      },
      {
        id: 5,
        name: "Wout van Aert",
        nationality: "BE",
        age: 30,
        birthdate: "1994-09-15",
        place_of_birth: "Herentals",
        height: 1.90,
        weight: 78,
        career_points: 4123,
        ranking_points: 1987,
        ranking_position: 5,
        specialities: {
          one_day_races: 1200,
          gc: 300,
          time_trial: 600,
          sprint: 800,
          climber: 200,
          hills: 900
        }
      }
    ]
  },
  {
    id: 3,
    name: "INEOS Grenadiers",
    nationality: "GB",
    status: "WT",
    abbreviation: "IGD",
    bike: "Pinarello",
    wins_count: 32,
    pcs_points: 6234,
    pcs_ranking_position: 3,
    uci_ranking_position: 3,
    logo: "https://via.placeholder.com/100x60/000080/FFFFFF?text=IGD",
    riders: [
      {
        id: 6,
        name: "Egan Bernal",
        nationality: "CO",
        age: 27,
        birthdate: "1997-01-13",
        place_of_birth: "Bogotá",
        height: 1.75,
        weight: 60,
        career_points: 3789,
        ranking_points: 1456,
        ranking_position: 12,
        specialities: {
          one_day_races: 300,
          gc: 900,
          time_trial: 350,
          sprint: 50,
          climber: 1000,
          hills: 600
        }
      },
      {
        id: 7,
        name: "Geraint Thomas",
        nationality: "GB",
        age: 38,
        birthdate: "1986-05-25",
        place_of_birth: "Cardiff",
        height: 1.83,
        weight: 71,
        career_points: 4567,
        ranking_points: 1234,
        ranking_position: 16,
        specialities: {
          one_day_races: 500,
          gc: 800,
          time_trial: 600,
          sprint: 100,
          climber: 400,
          hills: 550
        }
      }
    ]
  },
  {
    id: 4,
    name: "Quick-Step Alpha Vinyl",
    nationality: "BE",
    status: "WT",
    abbreviation: "QST",
    bike: "Specialized",
    wins_count: 41,
    pcs_points: 5987,
    pcs_ranking_position: 4,
    uci_ranking_position: 4,
    logo: "https://via.placeholder.com/100x60/0066CC/FFFFFF?text=QST",
    riders: [
      {
        id: 8,
        name: "Remco Evenepoel",
        nationality: "BE",
        age: 24,
        birthdate: "2000-01-25",
        place_of_birth: "Aalst",
        height: 1.71,
        weight: 61,
        career_points: 2987,
        ranking_points: 2134,
        ranking_position: 3,
        specialities: {
          one_day_races: 600,
          gc: 850,
          time_trial: 900,
          sprint: 200,
          climber: 500,
          hills: 700
        }
      },
      {
        id: 9,
        name: "Julian Alaphilippe",
        nationality: "FR",
        age: 32,
        birthdate: "1992-06-11",
        place_of_birth: "Saint-Amand-Montrond",
        height: 1.73,
        weight: 62,
        career_points: 3654,
        ranking_points: 1567,
        ranking_position: 10,
        specialities: {
          one_day_races: 1000,
          gc: 200,
          time_trial: 300,
          sprint: 400,
          climber: 600,
          hills: 900
        }
      }
    ]
  }
];

// Funciones para simular llamadas a API
export const getTeams = () => {
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve(teamsData.map(team => ({
        id: team.id,
        name: team.name,
        nationality: team.nationality,
        status: team.status,
        abbreviation: team.abbreviation,
        bike: team.bike,
        wins_count: team.wins_count,
        pcs_points: team.pcs_points,
        pcs_ranking_position: team.pcs_ranking_position,
        uci_ranking_position: team.uci_ranking_position,
        logo: team.logo,
        riders_count: team.riders.length
      })));
    }, 500); // Simular delay de red
  });
};

export const getTeamById = (teamId) => {
  return new Promise((resolve, reject) => {
    setTimeout(() => {
      const team = teamsData.find(t => t.id === parseInt(teamId));
      if (team) {
        resolve(team);
      } else {
        reject(new Error('Equipo no encontrado'));
      }
    }, 300);
  });
};

export const getRiderById = (riderId) => {
  return new Promise((resolve, reject) => {
    setTimeout(() => {
      let rider = null;
      for (const team of teamsData) {
        rider = team.riders.find(r => r.id === parseInt(riderId));
        if (rider) {
          rider.team = team.name;
          break;
        }
      }
      if (rider) {
        resolve(rider);
      } else {
        reject(new Error('Ciclista no encontrado'));
      }
    }, 300);
  });
};

export const searchRiders = (query) => {
  return new Promise((resolve) => {
    setTimeout(() => {
      const allRiders = [];
      teamsData.forEach(team => {
        team.riders.forEach(rider => {
          allRiders.push({
            ...rider,
            team: team.name,
            team_id: team.id
          });
        });
      });
      
      const filteredRiders = allRiders.filter(rider => 
        rider.name.toLowerCase().includes(query.toLowerCase()) ||
        rider.nationality.toLowerCase().includes(query.toLowerCase())
      );
      
      resolve(filteredRiders);
    }, 200);
  });
};
