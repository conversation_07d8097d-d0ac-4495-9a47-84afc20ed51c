/* Estilos generales */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  min-height: 100vh;
  color: #333;
}

.app {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

/* Header */
.app-header {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  padding: 2rem;
  text-align: center;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  position: sticky;
  top: 0;
  z-index: 100;
}

.app-header h1 {
  font-size: 2.5rem;
  margin-bottom: 0.5rem;
  background: linear-gradient(45deg, #667eea, #764ba2);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.app-header p {
  color: #666;
  font-size: 1.1rem;
  margin-bottom: 1.5rem;
}

/* Búsqueda */
.search-container {
  position: relative;
  max-width: 400px;
  margin: 0 auto 1rem;
}

.search-input {
  width: 100%;
  padding: 12px 20px;
  border: 2px solid #e0e0e0;
  border-radius: 25px;
  font-size: 1rem;
  outline: none;
  transition: all 0.3s ease;
}

.search-input:focus {
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.search-results {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: white;
  border-radius: 15px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
  max-height: 300px;
  overflow-y: auto;
  z-index: 1000;
  margin-top: 5px;
}

.search-result-item {
  padding: 12px 20px;
  cursor: pointer;
  border-bottom: 1px solid #f0f0f0;
  transition: background-color 0.2s ease;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.search-result-item:hover {
  background-color: #f8f9ff;
}

.search-result-item:last-child {
  border-bottom: none;
}

.search-result-item small {
  color: #666;
  font-size: 0.9rem;
}

/* Botón volver */
.back-button {
  background: linear-gradient(45deg, #667eea, #764ba2);
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 20px;
  cursor: pointer;
  font-size: 1rem;
  transition: transform 0.2s ease;
}

.back-button:hover {
  transform: translateY(-2px);
}

/* Main content */
.app-main {
  flex: 1;
  padding: 2rem;
  max-width: 1400px;
  margin: 0 auto;
  width: 100%;
}

/* Loading */
.loading {
  text-align: center;
  padding: 3rem;
  font-size: 1.2rem;
  color: white;
}

/* Grid de equipos */
.teams-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 2rem;
  margin-top: 2rem;
}

.team-card {
  background: white;
  border-radius: 20px;
  padding: 1.5rem;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  cursor: pointer;
  transition: all 0.3s ease;
  border: 2px solid transparent;
}

.team-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
  border-color: #667eea;
}

.team-header {
  display: flex;
  align-items: center;
  margin-bottom: 1rem;
}

.team-logo {
  width: 60px;
  height: 40px;
  object-fit: contain;
  margin-right: 1rem;
  border-radius: 8px;
}

.team-info h3 {
  font-size: 1.3rem;
  margin-bottom: 0.5rem;
  color: #333;
}

.team-info p {
  color: #666;
  font-size: 0.9rem;
}

.team-stats {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 0.5rem;
  margin: 1rem 0;
}

.stat {
  display: flex;
  justify-content: space-between;
  padding: 0.5rem 0;
  border-bottom: 1px solid #f0f0f0;
}

.stat-label {
  color: #666;
  font-size: 0.9rem;
}

.stat-value {
  font-weight: bold;
  color: #333;
}

.team-footer {
  margin-top: 1rem;
  padding-top: 1rem;
  border-top: 1px solid #f0f0f0;
  text-align: center;
}

.bike-brand {
  color: #667eea;
  font-weight: 500;
}

/* Detalles del equipo */
.team-detail {
  background: white;
  border-radius: 20px;
  padding: 2rem;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.team-detail-header {
  display: flex;
  align-items: center;
  margin-bottom: 2rem;
  padding-bottom: 2rem;
  border-bottom: 2px solid #f0f0f0;
}

.team-logo-large {
  width: 120px;
  height: 80px;
  object-fit: contain;
  margin-right: 2rem;
  border-radius: 12px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.team-detail-info h2 {
  font-size: 2.5rem;
  margin-bottom: 0.5rem;
  color: #333;
}

.team-detail-info > p {
  color: #666;
  font-size: 1.1rem;
  margin-bottom: 1.5rem;
}

.team-detail-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 1rem;
  margin-bottom: 1rem;
}

.stat-item {
  text-align: center;
  padding: 1rem;
  background: linear-gradient(135deg, #f8f9ff, #e8f0ff);
  border-radius: 12px;
}

.stat-number {
  display: block;
  font-size: 2rem;
  font-weight: bold;
  color: #667eea;
  margin-bottom: 0.5rem;
}

.stat-label {
  font-size: 0.9rem;
  color: #666;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.bike-info {
  color: #333;
  font-size: 1.1rem;
}

/* Sección de ciclistas */
.riders-section h3 {
  font-size: 1.8rem;
  margin-bottom: 1.5rem;
  color: #333;
}

.riders-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 1.5rem;
}

.rider-card {
  background: white;
  border: 2px solid #f0f0f0;
  border-radius: 15px;
  padding: 1.5rem;
  cursor: pointer;
  transition: all 0.3s ease;
}

.rider-card:hover {
  border-color: #667eea;
  transform: translateY(-3px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

.rider-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.rider-header h4 {
  font-size: 1.2rem;
  color: #333;
}

.rider-nationality {
  font-size: 1.5rem;
}

.rider-info p {
  margin: 0.5rem 0;
  color: #666;
}

.rider-specialities {
  margin-top: 1rem;
}

.speciality-bars {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.speciality {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.speciality span {
  font-size: 0.8rem;
  color: #666;
  min-width: 60px;
}

.bar {
  flex: 1;
  height: 6px;
  background: #f0f0f0;
  border-radius: 3px;
  overflow: hidden;
}

.fill {
  height: 100%;
  background: linear-gradient(90deg, #667eea, #764ba2);
  border-radius: 3px;
  transition: width 0.3s ease;
}

/* Detalles del ciclista */
.rider-detail {
  background: white;
  border-radius: 20px;
  padding: 2rem;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.rider-detail-header {
  display: flex;
  align-items: center;
  margin-bottom: 2rem;
  padding-bottom: 2rem;
  border-bottom: 2px solid #f0f0f0;
}

.rider-avatar {
  width: 100px;
  height: 100px;
  border-radius: 50%;
  background: linear-gradient(135deg, #667eea, #764ba2);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 2rem;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

.rider-initials {
  color: white;
  font-size: 2rem;
  font-weight: bold;
}

.rider-detail-info h2 {
  font-size: 2.5rem;
  margin-bottom: 0.5rem;
  color: #333;
}

.rider-detail-info > p {
  color: #666;
  font-size: 1.1rem;
  margin-bottom: 0.5rem;
}

.rider-basic-info {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
  margin-top: 1.5rem;
}

.info-item {
  display: flex;
  justify-content: space-between;
  padding: 0.75rem;
  background: #f8f9ff;
  border-radius: 8px;
}

.info-label {
  color: #666;
  font-weight: 500;
}

.info-value {
  color: #333;
  font-weight: bold;
}

/* Estadísticas del ciclista */
.rider-stats-section {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.ranking-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1.5rem;
}

.stat-card {
  text-align: center;
  padding: 2rem;
  background: linear-gradient(135deg, #f8f9ff, #e8f0ff);
  border-radius: 15px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
}

.stat-card .stat-number {
  display: block;
  font-size: 2.5rem;
  font-weight: bold;
  color: #667eea;
  margin-bottom: 0.5rem;
}

.stat-card .stat-label {
  font-size: 1rem;
  color: #666;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* Especialidades */
.specialities-section h3 {
  font-size: 1.5rem;
  margin-bottom: 1rem;
  color: #333;
}

.specialities-chart {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.speciality-item {
  display: grid;
  grid-template-columns: 120px 1fr 60px;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  background: #f8f9ff;
  border-radius: 10px;
}

.speciality-name {
  font-weight: 500;
  color: #333;
}

.speciality-bar {
  height: 10px;
  background: #e0e0e0;
  border-radius: 5px;
  overflow: hidden;
}

.speciality-fill {
  height: 100%;
  background: linear-gradient(90deg, #667eea, #764ba2);
  border-radius: 5px;
  transition: width 0.5s ease;
}

.speciality-value {
  text-align: right;
  font-weight: bold;
  color: #667eea;
}

/* Historial de equipos */
.teams-history-section h3 {
  font-size: 1.5rem;
  margin-bottom: 1rem;
  color: #333;
}

.teams-history {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.team-history-item {
  display: grid;
  grid-template-columns: 1fr auto auto;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  background: #f8f9ff;
  border-radius: 8px;
  border-left: 4px solid #667eea;
}

.team-name {
  font-weight: 500;
  color: #333;
}

.team-period {
  color: #666;
  font-size: 0.9rem;
}

.team-class {
  background: #667eea;
  color: white;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-size: 0.8rem;
  font-weight: bold;
}

/* Responsive */
@media (max-width: 768px) {
  .app-header {
    padding: 1rem;
  }

  .app-header h1 {
    font-size: 2rem;
  }

  .app-main {
    padding: 1rem;
  }

  .teams-grid {
    grid-template-columns: 1fr;
  }

  .team-detail-header,
  .rider-detail-header {
    flex-direction: column;
    text-align: center;
  }

  .team-logo-large {
    margin-right: 0;
    margin-bottom: 1rem;
  }

  .rider-avatar {
    margin-right: 0;
    margin-bottom: 1rem;
  }

  .team-detail-stats,
  .ranking-stats {
    grid-template-columns: 1fr 1fr;
  }

  .rider-basic-info {
    grid-template-columns: 1fr;
  }

  .speciality-item {
    grid-template-columns: 1fr;
    text-align: center;
  }

  .team-history-item {
    grid-template-columns: 1fr;
    text-align: center;
  }
}