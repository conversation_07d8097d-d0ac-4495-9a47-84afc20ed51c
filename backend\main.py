from fastapi import FastAP<PERSON>, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from typing import List, Dict, Any, Optional
import uvicorn
from procyclingstats import Team, Ranking
import logging

# Configurar logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = FastAPI(
    title="KGR Cycling API",
    description="API para obtener información de equipos de ciclismo y ciclistas usando ProCyclingStats",
    version="1.0.0"
)

# Configurar CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:5173", "http://localhost:3000"],  # Vite y Create React App
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

@app.get("/")
async def root():
    """Endpoint raíz de la API"""
    return {"message": "KGR Cycling API - ProCyclingStats Integration"}

@app.get("/api/teams", response_model=List[Dict[str, Any]])
async def get_teams():
    """
    Obtiene la lista de equipos desde el ranking de equipos de ProCyclingStats
    """
    try:
        logger.info("Obteniendo ranking de equipos...")
        # Usar el ranking de equipos para obtener la lista
        ranking = Ranking("rankings/me/teams")
        teams_data = ranking.team_ranking()
        
        # Formatear los datos para el frontend
        teams = []
        for team in teams_data:
            teams.append({
                "name": team.get("team_name"),
                "url": team.get("team_url"),
                "nationality": team.get("nationality"),
                "rank": team.get("rank"),
                "points": team.get("points"),
                "class": team.get("class")
            })
        
        logger.info(f"Se obtuvieron {len(teams)} equipos")
        return teams
    
    except Exception as e:
        logger.error(f"Error obteniendo equipos: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error obteniendo equipos: {str(e)}")

@app.get("/api/teams/{team_url:path}", response_model=Dict[str, Any])
async def get_team_details(team_url: str):
    """
    Obtiene los detalles de un equipo específico incluyendo sus ciclistas
    """
    try:
        logger.info(f"Obteniendo detalles del equipo: {team_url}")
        
        # Crear objeto Team con la URL
        team = Team(team_url)
        
        # Obtener información básica del equipo
        team_info = {
            "name": team.name(),
            "nationality": team.nationality(),
            "status": team.status(),
            "abbreviation": team.abbreviation(),
            "bike": team.bike(),
            "wins_count": team.wins_count(),
            "pcs_points": team.pcs_points(),
            "pcs_ranking_position": team.pcs_ranking_position(),
            "uci_ranking_position": team.uci_ranking_position()
        }
        
        # Obtener lista de ciclistas
        riders = team.riders()
        
        # Formatear datos de ciclistas
        formatted_riders = []
        for rider in riders:
            formatted_riders.append({
                "name": rider.get("rider_name"),
                "url": rider.get("rider_url"),
                "nationality": rider.get("nationality"),
                "age": rider.get("age"),
                "since": rider.get("since"),
                "until": rider.get("until"),
                "career_points": rider.get("career_points"),
                "ranking_points": rider.get("ranking_points"),
                "ranking_position": rider.get("ranking_position")
            })
        
        result = {
            "team_info": team_info,
            "riders": formatted_riders
        }
        
        logger.info(f"Detalles obtenidos para {team_info['name']} con {len(formatted_riders)} ciclistas")
        return result
    
    except Exception as e:
        logger.error(f"Error obteniendo detalles del equipo {team_url}: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error obteniendo detalles del equipo: {str(e)}")

@app.get("/api/riders/{rider_url:path}", response_model=Dict[str, Any])
async def get_rider_details(rider_url: str):
    """
    Obtiene los detalles de un ciclista específico
    """
    try:
        logger.info(f"Obteniendo detalles del ciclista: {rider_url}")
        
        from procyclingstats import Rider
        
        # Crear objeto Rider con la URL
        rider = Rider(rider_url)
        
        # Obtener información del ciclista
        rider_info = {
            "name": rider.name(),
            "birthdate": rider.birthdate(),
            "place_of_birth": rider.place_of_birth(),
            "nationality": rider.nationality(),
            "height": rider.height(),
            "weight": rider.weight(),
            "image_url": rider.image_url(),
            "teams_history": rider.teams_history(),
            "points_per_season_history": rider.points_per_season_history(),
            "points_per_speciality": rider.points_per_speciality()
        }
        
        logger.info(f"Detalles obtenidos para {rider_info['name']}")
        return rider_info
    
    except Exception as e:
        logger.error(f"Error obteniendo detalles del ciclista {rider_url}: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error obteniendo detalles del ciclista: {str(e)}")

if __name__ == "__main__":
    uvicorn.run(app, host="0.0.0.0", port=8000)
